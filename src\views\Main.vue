<script lang="ts" setup>
import { ref } from 'vue'
import Home from './home/<USER>'
import Icon from '@/components/common/Icon.vue'
import ReportList from './report/ReportList.vue'
import AttentionList from './attention/AttentionList.vue'
import My from './my/My.vue'

defineOptions({
    name: 'MainPage',
})

const active = ref(0)

const tabs = [
    {
        id: 0,
        label: '首页',
        icon: 'icon-shouye',
    },
    {
        id: 1,
        label: '关注',
        icon: 'icon-a-Component30',
    },
    {
        id: 2,
        label: '报告',
        icon: 'icon-a-Component31',
    },
    {
        id: 3,
        label: '我的',
        icon: 'icon-a-Component32',
    },
]
</script>

<template>
    <div class="main">
        <div class="container">
            <Home v-if="active === 0" />
            <AttentionList v-if="active === 1" />
            <ReportList v-if="active === 2" />
            <My v-if="active === 3" />
        </div>
        <van-tabbar v-model="active" :safe-area-inset-bottom="true" id="main-tabbar">
            <van-tabbar-item v-for="item in tabs" :key="item.id">
                <span
                    :class="{
                        active: active === item.id,
                    }"
                >
                    {{ item.label }}
                </span>
                <template #icon="props">
                    <Icon
                        style="cursor: pointer"
                        :icon="item.icon"
                        :color="`${props.active ? 'var(--main-blue-)' : 'var(--table-bg-)'}`"
                        :size="20"
                    />
                </template>
            </van-tabbar-item>
        </van-tabbar>
    </div>
</template>

<style lang="scss" scoped>
.main {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100%;
}

.container {
    flex: 1;
    overflow: auto;
}

:deep(.van-tabbar__placeholder) {
    background-color: transparent !important;
}

.active {
    color: var(--main-blue-);
    font-weight: 500;
}

.un-active {
    color: var(--table-bg-);
}

.label {
    color: var(--table-bg-);
}
</style>
