<template>
    <div>
        <div v-for="(item, index) in proList" :key="index" class="border-bottom tb-padding-16">
            <div class="space-between display-flex top-bottom-center " @click="showMore(item, index)">
                <div class="display-flex top-bottom-center flex-1">
                    <div class="font-16 font-bold r-margin-16 ">
                        {{ item.indicatorName || item.riskName || item.associatedType + '关联风险检查' }}
                    </div>

                    <div style="min-width: 50px;" class="display-flex top-bottom-center">
                        <van-tag v-if="progressType != 'related' && item.level != null" :type="getTagColor(item.level)"
                                 size="medium">{{ item.level == 100 ? '高风险' : item.level == 50 ? '中风险' : item.level == 1 ?
                                     '低风险'
                                     :
                                     item.level == -1 ? '不适用' : '无风险' }}</van-tag>
                        <van-tag v-else-if="progressType == 'related' && item.list.length == 0" :type="getTagColor(0)"
                                 size="medium">无风险</van-tag>

                    </div>


                </div>
                <div v-if="progressType == 'invoice' && item.level < 1"></div>
                <div v-else-if="progressType == 'related' && item.list.length || progressType == 'tax' || progressType == 'basic' || progressType == 'finance' || progressType == 'invoice' && item.level > 0"
                     class="display-flex top-bottom-center" style="min-width: 50px;">
                    <div class="color-two-grey font-12 r-margin-12 color-two-grey text-nowrap" style="min-width: 20px;">
                        {{ currentItem(index) == false ? '展开' : '收起' }}
                    </div>

                    <Icon v-if="currentItem(index) !== false" icon="icon-a-Frame1000003597" :size="18"
                          color="#B3B3B3" />
                    <Icon v-else icon="icon-a-Frame1171276219" :size="18" color="#B3B3B3" />
                </div>

            </div>
            <div class="border-item ">
                <div v-if="currentItem(index)" class="t-margin-16">
                    <!-- 发票 -->
                    <div v-if="progressType == 'invoice'">
                        <div class="display-flex top-bottom-center font-12 margin-bottom-24">
                            <div class="color-two-grey">检测数据区间：</div>
                            <div>{{ item.interval }}</div>
                        </div>
                        <div class="margin-bottom-24 font-12 opacity-8 display-flex"
                             v-for="(event, i) in item.riskEvent" :key="i">
                            <image src="../../static/common/icon/dot.png" class="dot r-margin-16 margin-top-10">
                            </image>
                            <div>{{ event }}</div>
                        </div>
                        <div
                            class="risk-point lr-padding-8 tb-padding-8 border-radius-8 display-flex top-bottom-center margin-bottom-24">
                            <div class="color-red font-bold font-12 r-margin-16">风险暴露点</div>
                            <div class="font-12 color-white">
                                {{ item.riskPoint }}
                            </div>
                        </div>
                    </div>
                    <!-- 基础项目 -->
                    <div v-else-if="progressType == 'basic'">
                        <div class="display-flex top-bottom-center margin-bottom-24">
                            <div class="flex-1 display-flex flex-column top-bottom-center">
                                <div class="display-flex top-bottom-center">
                                    <div class="font-size-36 font-bold r-margin-6">{{ item.total }}
                                    </div>
                                </div>
                                <div class="font-12 color-two-grey">总命中数</div>
                            </div>
                            <div class="flex-1 display-flex flex-column top-bottom-center">
                                <div class="display-flex top-bottom-center">
                                    <div class="font-size-36 font-bold r-margin-6">
                                        {{ item.oneTotal }}
                                    </div>
                                </div>
                                <div class="font-12 color-two-grey">近一年命中数</div>
                            </div>
                        </div>
                    </div>
                    <!-- 财务/税务 -->
                    <div v-else-if="progressType == 'finance' || progressType == 'tax'">
                        <div class="display-flex top-bottom-center font-12 margin-bottom-24">
                            <div class="color-two-grey">检测数据区间：</div>
                            <div>{{ item.year || item.interval || '-' }}</div>
                        </div>
                        <!-- 明细 -->
                        <div class="display-flex top-bottom-center margin-bottom-20 space-between">
                            <div :class="progressType == 'finance' ? 'flex-1' : ''"
                                 class="display-flex flex-column top-bottom-center">
                                <div class="display-flex top-bottom-center">
                                    <div class="font-bold r-margin-6"
                                         :class="item.value && item.value.length > 8 || item.indexValue && item.indexValue.length > 8 ? 'font-size-28' : item.value && item.value.length > 12 || item.indexValue && item.indexValue.length > 12 ? 'font-12' : 'font-size-32'">
                                        {{ item.value || item.indexValue || '-' }}
                                    </div>
                                </div>
                                <div class="font-12 color-two-grey">指标值</div>
                            </div>


                            <div :class="progressType == 'finance' ? 'flex-1' : ''"
                                 class="display-flex flex-column top-bottom-center">
                                <div class="display-flex top-bottom-center">
                                    <div class="font-bold r-margin-6 white-space"
                                         :class="item.reference && item.reference.length > 8 || item.referenceValue && item.referenceValue.length > 8 ? 'font-size-28' : item.reference && item.reference.length > 12 || item.referenceValue && item.referenceValue.length > 12 ? 'font-12' : 'font-size-32'">
                                        {{ item.reference || item.referenceValue || '-' }}
                                    </div>
                                </div>
                                <div class="font-12 color-two-grey">参考值</div>
                            </div>
                            <div :class="progressType == 'finance' ? 'flex-1' : ''"
                                 class="display-flex flex-column top-bottom-center">
                                <div class="display-flex top-bottom-center">
                                    <div class="font-size-32 font-bold r-margin-6"
                                         :class="item.deviate && item.deviate.length > 8 || item.deviationValue && item.deviationValue.length > 8 ? 'font-size-28' : item.deviate && item.deviate.length > 12 || item.deviationValue && item.deviationValue.length > 12 ? 'font-12' : 'font-size-32'">
                                        {{ item.deviate || item.deviationValue || '-' }}
                                    </div>
                                </div>

                                <div class="font-12 color-two-grey">偏离检查</div>
                            </div>
                        </div>
                        <div v-if="progressType == 'finance'"
                             class="risk-point lr-padding-8 tb-padding-8 border-radius-8 display-flex top-bottom-center margin-bottom-24">
                            <div class="color-red font-bold font-12 r-margin-16">原因推测</div>
                            <div class="font-12">
                                {{ item.causation }}
                            </div>
                        </div>
                    </div>
                    <!-- 关联项目 -->
                    <div v-else-if="progressType == 'related'">
                        <div v-for="(relateItem, i) in item.list" :key="i">
                            <div class="display-flex top-bottom-center">
                                <div class="font-size-28 r-margin-16">{{ relateItem.associatedCompany }}
                                </div>
                                <div style="min-width: 50px;">
                                    <van-tag :type="getTagColor(item.level)" size="medium">
                                        {{ relateItem.level == 100 ? '高风险' : relateItem.level == 50 ? '中风险' :
                                            relateItem.level == 1 ? '低风险' : relateItem.level == -1 ? '不适用' : '无风险' }}
                                    </van-tag>
                                </div>
                            </div>
                            <div class="display-flex tb-padding-12 flex-wrap">
                                <!-- <div class="color-yellow-main font-12 flex-1">{{relateItem.associatedType}}</div> -->
                                <!-- <div class="margin-left-30"> -->
                                <div class="b-margin-10" style="width: 50%;" v-for="(event, i) in relateItem.riskEvent"
                                     :key="i">
                                    <div class="display-flex top-bottom-center">
                                        <image src="../../static/common/icon/dot.png" class="dot r-margin-16">
                                        </image>
                                        <div class="font-12 opacity-8">{{ event }}</div>
                                    </div>
                                </div>
                                <!-- </div> -->
                            </div>
                            <div
                                class="risk-point lr-padding-8 tb-padding-8 border-radius-8 display-flex top-bottom-center margin-bottom-24">
                                <div class="color-red font-bold font-12 r-margin-16">风险暴露点</div>
                                <div class="font-12 color-two-grey">
                                    {{ relateItem.riskExposure || '-' }}
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <div class="display-flex top-bottom-center font-14 font-bold  left-right-center t-margin-30 b-margin-30"
             v-if="riskList.length > 0 && noRiskList.length > 0 || noCollectList.length > 0" @click="expand">
            <div class="r-margin-12" v-if="!noCollect">{{ expandFlag ? '收起' : '查看' }}剩余{{ noRiskList.length }}项无风险项
            </div>
            <div class="r-margin-12" v-else>
                {{ expandFlag ? '收起' : '查看' }}剩余{{ props.dataList.length - noCollectList.length }}项指标项</div>

            <Icon v-if="expandFlag" icon="icon-a-Frame1000003597" :size="18" color="#B3B3B3" />
            <Icon v-else icon="icon-a-Frame1171276219" :size="18" color="#B3B3B3" />

        </div>
    </div>
</template>

<script setup>
import {
    defineProps,
    ref,
    reactive,
    onMounted,
    watch,
    inject
} from 'vue'
import Icon from '@/components/common/Icon.vue'
const props = defineProps({
    dataList: {
        type: Array,
    },
    progressType: {
        type: String,
    },
    noCollect: {
        type: Boolean,
    }
})

const mainColor = inject('mainColor')

console.log(mainColor)


watch(props, () => {
    init()
})

// 当前行展开收起
const visibilityMap = ref([])
const showMore = (item, index) => {
    if (item.level < 1 && props.progressType === 'invoice') {
        return
    }
    visibilityMap.value[index] = !visibilityMap.value[index]
}
const currentItem = (index) => {
    return visibilityMap.value[index] || false
}

const noRiskList = reactive([])
const riskList = reactive([])
const proList = ref([])
let noCollectList = reactive([])
const expandFlag = ref(false)
const init = () => {
    for (let item of props.dataList) {
        if (item.level && item.level > 0) {
            riskList.push(item)
        } else {
            noRiskList.push(item)
        }
    }

    // 如果没有采集
    if (props.noCollect) {
        noCollectList = props.dataList.slice(0, 5)
        proList.value = noCollectList
    } else {
        // 如果采集了有风险项
        if (riskList.length) {
            proList.value = riskList
        } else {
            proList.value = props.dataList
        }

    }
    // console.log('---------------',props.progressType,proList.value)
}

onMounted(() => {
    init()
})

const expand = () => {
    expandFlag.value = !expandFlag.value
    if (props.noCollect) {
        if (expandFlag.value) {
            proList.value = props.dataList
        } else {
            proList.value = noCollectList
        }
    } else {
        if (expandFlag.value) {
            proList.value = [...riskList, ...noRiskList]
        } else {
            proList.value = riskList
        }
    }

}

const getTagColor = (level) => {
    switch (level) {
    case 100:
        return 'danger'
    case 50:
        return 'warning'
    case 1:
        return 'success'
    case 0:
        return 'primary'
    default:
        return ''

    }
}
</script>

<style lang="scss" scoped>
::v-deep .uni-tag {
    min-width: 100rpx;
}

::v-deep .uni-tag-text--small {
    font-weight: 800 !important;
}


.border-item {
    border-bottom: 1rpx solid;
    border-bottom-color: rgba(150, 180, 255, 0.2);
}

.array-icon {
    width: 18rpx;
    height: 25rpx;
}

.dot {
    height: 16rpx;
    width: 16rpx;
    min-width: 16rpx;
}

.risk-point {}
</style>