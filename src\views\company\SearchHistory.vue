<template>
    <div class="tb-padding-12 lr-padding-16">
        <div @click="jump2Search(null)"
             class="display-flex top-bottom-center color-three-grey border tb-padding-8 lr-padding-12 border-radius-4 font-14">
            <div><van-icon name="search" /></div>
            <div class="l-margin-6">请输入您要检测的企业名称</div>
        </div>
        <div class="t-margin-12" v-if="historyInput.length">
            <div class="display-flex space-between top-bottom-center">
                <div class="font-14 color-two-grey">最近搜索</div>
                <div @click="clearCache"><van-icon name="delete-o" /></div>
            </div>
            <div class="display-flex flex-wrap t-margin-12">
                <div v-for="item in historyInput" :key="item"
                     class="border border-radius-20 tb-padding-6 lr-padding-12 b-margin-6 font-14 r-margin-12"
                     @click="jump2Search(item)">
                    {{ item }}
                </div>
            </div>
        </div>

        <div class="t-margin-20">
            <div class="font-14 color-blue font-weight-400 b-margin-8">最近检测</div>
            <div class=" lr-padding-16 back-color-2b-gradient-blue-10 border-radius-8">
                <div v-for="item in crmList" :key="item.id"
                     class="display-flex top-bootom-center space-between border-bottom tb-padding-6 ">
                    <div>
                        <div class="font-14">
                            {{ item.name }}
                        </div>
                        <div class="font-12 color-blue">
                            最近体检：{{ item.basicScore || '-' }}分
                        </div>
                    </div>
                    <div>
                        <div class="btn" @click="showCompanyDetail(item)">再次体检</div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import type { Ref } from 'vue'

import type { ILeadData } from '@/types/lead'

import { useRouter } from 'vue-router'

import { getItem, setItem } from '@/utils/storage.ts'

import crmService from '@/service/crmService'
import companyService from '@/service/companyService'

const router = useRouter()

const historyInput = ref<string[]>([] as string[])

const crmList: Ref<ILeadData[]> = ref([])


onMounted(() => {
    console.log('33344')
    let res = getItem('historySearch')
    console.log(res)
    if (res) {
        historyInput.value = res
    }
    getCrmList()
})


const jump2Search = (val: string | null) => {
    router.push({
        name: 'searchCompany',
        query: {
            searchKey: val
        }
    })
}
const getCrmList = () => {
    crmService.crmList({
        page: 1,
        pageSize: 5
    }).then(res => {
        console.log(res)
        crmList.value = res.data || []
    })
}

const clearCache = () => {
    setItem('historySearch', [])
    historyInput.value = []
}
const showCompanyDetail = (item: ILeadData) => {
    companyService.refresh({
        socialCreditCode: item.socialCreditCode,
        companyName: item.companyName,
    }).then(() => {
        router.push({
            name: 'companyDetail',
            query: {
                socialCreditCode: item.socialCreditCode,
                companyName: item.companyName
            }
        })
    })

}
</script>

<style></style>
