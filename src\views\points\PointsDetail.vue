<template>
    <div class="content">
        <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="已经到底啦~"
            :offset="100"
            @load="onLoad"
        >
            <van-cell v-for="item in detailList" :key="item.id" style="min-height: 10%">
                <div class="display-flex space-between top-bottom-center" style="width: 100%; height: 100%">
                    <div class="display-flex flex-column left-right-center" style="align-items: flex-start">
                        <div class="font-14 color-black text-nowrap text-ellipsis" style="max-width: 250px">
                            <span v-if="item.type === 1">成功邀请 {{ item.relationUserName }}注册</span>
                            <span v-if="item.type === 2 || item.type === 3">{{ item.msg }}</span>
                            <span v-if="item.type === 100 && item.goods && item.goods.length > 0">
                                兑换了 {{ item.goods[0].name }}
                            </span>
                        </div>
                        <div class="color-two-grey">
                            完成时间：{{
                                item.createTime ? moment(item.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'
                            }}
                        </div>
                    </div>
                    <div class="font-14 font-weight-500" style="color: #ff854c">
                        <span>{{ item.amount }}积分</span>
                    </div>
                </div>
            </van-cell>
        </van-list>
    </div>
</template>

<script lang="ts" setup>
import { ref, getCurrentInstance, onMounted, reactive } from 'vue'
import type { IOrderInviteRecordParams, IOrderInviteRecordListItem } from '@/types/order'
import orderService from '@/service/orderService'

const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment
let queryParams = reactive<IOrderInviteRecordParams>({
    page: 1,
    pageSize: 10,
})

const detailList = ref<IOrderInviteRecordListItem[]>([])
const loading = ref(false)
const finished = ref(false)

const search = async (params: IOrderInviteRecordParams) => {
    const res = await orderService.orderInviteNewInviteRecord(params)
    detailList.value.push(...res.data)
    return res
}

const onLoad = () => {
    setTimeout(async () => {
        let cuurentPage = queryParams.page
        const res = await search(queryParams)
        queryParams.page += 1
        loading.value = false
        console.log(res)
        if (cuurentPage === res.totalPages) {
            finished.value = true
        }
    }, 1000)
}
onMounted(() => {
    // search(queryParams)
})
</script>

<style lang="scss" scoped>
.content {
    width: 100vw;
    min-height: 100vh;
    box-sizing: border-box;
    background-image: url('@/assets/images/points/pointsdetail-bg.png');
    background-size: 100% 100%;
    :deep(.van-search) {
        padding: 12px 0;
    }
    :deep(.van-search__content) {
        background-color: #fff;
    }
    :deep(.van-list) {
        background-color: transparent;
    }
    :deep(.van-cell) {
        background-color: transparent;
    }
}

.cell-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 100%;
}

</style>
