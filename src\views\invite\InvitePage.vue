<template>
    <div class="height-100 back-color-common lr-padding-16 oh display-flex flex-column space-between">
        <div class="h-225 t-margin-19 invite-ad relative">
            <div class="font-14 color-two-grey absolute" style="right: 0; top: -12px" @click="handleOpenIntroduce">活动规则</div>
        </div>
        <div
            class="flex-1 t-margin-12 border-radius-8 tb-padding-16 display-flex flex-column top-bottom-center"
            style="background-color: rgba(238, 246, 255, 0.5);"
        >
            <img class="w-232 h-22" src="@/assets/images/invite-record-icon.png" alt="邀请记录" />
            <div
                class="t-margin-8 w-293 h-40 font-14 color-black text-center lh-40"
                style="background: #eff5ffa1; box-shadow: 2px -4px 19.7px 0px #dff0ff inset; backdrop-filter: blur(5px)"
            >
                已邀请{{inviteNum}}人，累积获得：{{pointSummary}}积分
            </div>
            <div class="width-100 flex-1 t-margin-8 font-14 border-box">
                <div v-if="list.length>0" class="display-flex flex-column width-100 height-100">
                    <div class="display-flex h-20 tb-padding-8">
                        <div class="w-90 text-center font-weight-400 color-two-grey" v-for="(item,index) in tableHeader" :key="index">{{ item.title }}</div>
                    </div>
                    <div class="flex-1 max-height-340 overflow-y-auto" @scroll="handleScroll" >
                        <div v-for="(item,index) in list" :key="index" class="display-flex h-20 tb-padding-8 lh-20">
                            <div class="w-90 text-center font-weight-400 color-black">{{ item?.relationUserMobile || '-' }}</div>
                            <div class="w-90 text-center font-weight-400 color-black">注册成功</div>
                            <div class="w-90 text-center font-weight-400 color-black">{{ parseTime(item?.createTime, '{y}-{m}-{d}')  || '-'}}</div>
                            <div class="w-90 text-center font-weight-400 color-black">+25</div>
                        </div>
                        <div v-if="listLoading" class="h-20 tb-padding-8 text-center">加载中</div>
                        <div v-if="listFinished" class="h-20 tb-padding-8 text-center">没有更多了</div>
                    </div>
                </div>
                <div v-else class="t-margin-24 color-two-grey text-center">
                    暂无数据，快去邀请好友吧~
                </div>
            </div>
        </div>
        <div class="t-margin-12 b-margin-12">
            <van-button type="primary" class="h-46 width-100 border-radius-8" @click="handleInvite()" style="background: linear-gradient(to right, #3c74eb, #95d5f4); border: none;">立即邀请好友</van-button>
        </div>
        <van-overlay :show="introduceVisible">
            <div class="display-flex center width-100 height-100">
                <div class="display-flex flex-column top-bottom-center">
                    <img class="w-280 h-403" src="@/assets/images/invite-new-introduce.png" alt="活动规则">
                    <div class="w-24 h-24 t-margin-24" @click="introduceVisible = false">
                        <Icon color="rgba(255, 255, 255, 0.7)" icon="icon-a-Frame1171276285" size="24"></Icon>
                    </div>
                </div>
            </div>
        </van-overlay>
    </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import orderService from '@/service/orderService'
import Icon from '@/components/common/Icon.vue'
import type { IOrderInviteRecordParams } from '@/types/order'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import { parseTime } from '@/utils/parse-time'
import type { IOrderInviteRecordListItem } from '@/types/order'
const store = useStore<RootState>()
const router = useRouter()
const userInfo = computed(() => {
    const { account } = store.state.user
    const { user } = account || {}
    return user
})
const userId = computed(() => {
    const { account } = store.state.user || {}
    const { user } = account || {}
    const { id } = user || {}
    return id
})
const introduceVisible = ref(false)
const tableHeader = [
    {
        title:'好友ID'
    },
    {
        title:'好友状态'
    },
    {
        title:'时间'
    },
    {
        title:'积分'
    }
]
const list = ref<IOrderInviteRecordListItem[]>([])
const listLoading = ref(false)
const listFinished = ref(false)
const inviteNum = ref(0)
const pointSummary = ref(0)
const queryParams = ref<IOrderInviteRecordParams>({
    page: 0,
    pageSize: 10,
    type: 1,
    userId: userId.value || '67e261d05c3869503a0d4179',

})
const initData = async () => {
    queryParams.value.page += 1
    let res = await orderService.orderInviteNewInviteRecord(queryParams.value)
    listLoading.value = false
    const { data, total } = res
    if (data.length > 0) {
        list.value.push(...data)
    }
    if (data.length === total) {
        listFinished.value = true
    }
}
const getInviteSummary = async () => {
    let res = await orderService.orderInvitedSummary({type:1})
    const { number, totalPoints } = res
    inviteNum.value = number || 0
    pointSummary.value = Math.abs(totalPoints) || 0
    
}
const handleOpenIntroduce = () => {
    introduceVisible.value = !introduceVisible.value
}
const handleInvite = () => {
    router.push({
        name:'invite-friend'    
    })
}
onMounted(() => {
    console.log('userInfo',userInfo.value) // 等存储了更新userId tenantId
    initData()
    getInviteSummary()
})
let debounceTimer: ReturnType<typeof setTimeout> | null = null
const handleScroll = (event:Event) => {
    const container = event.target as HTMLElement // 获取滚动容器
    // scrollHeight 容器内部高度
    // scrollTop 滚动的距离
    // clientHeight 容器的(可视)高度
    // console.log(container.scrollHeight - container.scrollTop,container.clientHeight);
    const bottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 1
    if (bottom) {
        if(listFinished.value) return
        listLoading.value = true
        if (debounceTimer) {
            clearTimeout(debounceTimer)
        }
        debounceTimer = setTimeout(() => {
            initData()
        },500)
    }   
}

</script>
<style scoped lang="scss">
.invite-ad {
    background-image: url('@/assets/images/invite-ad.png');
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
}
</style>
