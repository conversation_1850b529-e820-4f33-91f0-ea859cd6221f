import http from '@/axios'
import type { SearchCollectLogResponse, CollectLogParams, ExportParams, DownlodaReportParams, DownloadReportResponse, ReportItem } from '@/types/report'
import type { companyBasicData } from '@/types/indicator'

export default {
    collectPage(data: CollectLogParams): Promise<SearchCollectLogResponse> {
        return http.get(`/api/zhenqi-report/collect/page`, {
            params: data,
            hideError: true,
        })
    },

    collectExport(data: ExportParams): Promise<Blob> {
        return http.post('/api/zhenqi-report/collect/export', data, {
            responseType: 'blob',
            hideError: true,
        })
    },

    collectGetReportUrl(data: DownlodaReportParams): Promise<DownloadReportResponse> {
        const queryString = new URLSearchParams(data as unknown as Record<string, string>).toString()
        return http.get(`/api/zhenqi-report/collect/get-report-url?${queryString}`, {
            hideError: true,
        })
    },
    getBasicResult(data: { socialCreditCode: string }): Promise<companyBasicData> {
        return http.get(`/api/zhenqi-report/indicator/get-basic-result`, {
            params: data,
            hideError: true,
        })
    },
    getFinanceResult(data: { socialCreditCode: string }): Promise<companyBasicData> {
        return http.get(`/api/zhenqi-report/indicator/get-finance-result`, {
            params: data,
            hideError: true,
        })
    },
    getTaxResult(data: { socialCreditCode: string }) {
        return http.get(`/api/zhenqi-report/indicator/get-tax-result`, {
            params: data,
            hideError: true,
        })
    },
    getInvoiceResult(data: { socialCreditCode: string }) {
        return http.get(`/api/zhenqi-report/indicator/get-invoice-result`, {
            params: data,
            hideError: true,
        })
    },
    getRelatedResult(data: { socialCreditCode: string }) {
        return http.get(`/api/zhenqi-report/indicator/get-related-result`, {
            params: data,
            hideError: true,
        })
    },
    getOperateStateResult(data: { socialCreditCode: string }) {
        return http.get(`/api/zhenqi-report/indicator/get-operate-state-result`, {
            params: data,
            hideError: true,
        })
    },
    getLifeCycleResult(data: { socialCreditCode: string }) {
        return http.get(`/api/zhenqi-report/indicator/get-life-cycle-result`, {
            params: data,
            hideError: true,
        })
    },
    sendEmailReport(body: { companyName: string, socialCreditCode: string, reportId: string, taxRequestId: string, email: string, reportType: string }): Promise<void> {
        return http.post(`/api/zhenqi-report/report/send-email-report`, body)
    },
    getReportList(body: { socialCreditCode: string }): Promise<ReportItem[]> {
        return http.get(`/api/zhenqi-report/report/get-report-list`, {
            params: body
        })
    },
}