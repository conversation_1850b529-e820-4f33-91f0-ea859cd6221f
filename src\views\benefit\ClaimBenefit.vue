<template>
    <div class="height-100 back-color-common display-flex flex-column space-between lr-padding-16">
        <div class="display-flex flex-column top-bottom-center">
            <img class="w-36 h-32 t-margin-40" src="@/assets/images/zqy-logox2.png" alt="臻企云logo">
            <div class="font-16 font-weight-500 color-black t-margin-17">{{ username }} 赠送您</div>
            <div class="t-margin-25 width-100 border-radius-8 tb-padding-24 border-color-blue " style="background: linear-gradient(99.4deg, #CADBFF 2.86%, #E7F7FF 101.71%);">
                <div class="font-20 font-weight-500 !color-blue text-center">“{{serviceInfo?.service.service_name}}”X{{ quantity }}</div>
                <div class="font-12 !color-blue text-center t-margin-4">有效时间：{{serviceInfo?.expire_time}}</div>
            </div>
        </div>
        <div class="b-margin-12">
            <van-button 
                v-if="isExpire" 
                type="primary" 
                class="h-46 width-100 border-radius-8" 
                style="background: linear-gradient(to right, #9EBAF5, #CAEAF9); 
                border: none;" 
                disabled
            > {{ '已过期 '}} 
            </van-button>
            <van-button 
                v-else-if="isReceive" 
                type="primary" 
                class="h-46 width-100 border-radius-8" 
                style="background: linear-gradient(to right, #9EBAF5, #CAEAF9); 
                border: none;" 
                disabled
                @click="isReceive ? receiveBenefit : null"
            >{{ '权益已被领取' }}
            </van-button>
            <van-button 
                v-else 
                type="primary" 
                class="h-46 width-100 border-radius-8" 
                style="background: linear-gradient(to right, #9EBAF5, #CAEAF9); 
                border: none;" 
                @click="receiveBenefit"
            >{{ '立即领取' }}
            </van-button>
        </div>
    </div>
</template>
<script lang='ts' setup>
import { onMounted, ref, computed, onBeforeMount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import type { IServiceOrderResponseItem } from '@/types/order'
import { showToast } from 'vant'
import orderService from '@/service/orderService'

const router = useRouter()
const store = useStore<RootState>()
const userInfo = computed(() => {
    const { account } = store.state.user 
    const { user } = account || {}
    return user
})
const route = useRoute()
const username = ref('')
const serviceInfo = ref<IServiceOrderResponseItem>()
const expireTime = ref('')
const quantity = ref('')
const type = ref('xs')
const id = ref('')
const isExpire = ref(false)
const isReceive = ref(false)

const init = () => {
    username.value = route.query.username as string
    console.log('所有查询参数:', route.query)
    const serviceInfoParam = route.query.serviceInfo as string
    if (serviceInfoParam) {
        try {
            
            console.log('id:', id.value)
            serviceInfo.value = JSON.parse(decodeURIComponent(serviceInfoParam)) as IServiceOrderResponseItem
            // console.log('解析后的 serviceInfo:', serviceInfo.value)
        } catch (error) {
            console.error('解析 serviceInfo 失败:', error)
        }
    }

    quantity.value = route.query.quantity as string
    expireTime.value = route.query.expireTime as string
    type.value = route.query.type as string
    id.value = encodeURIComponent(route.query.id as string)
    // console.log('username:', username.value)
    // console.log('quantity:', quantity.value)
    // console.log('expireTime:', expireTime.value)
    // console.log('type:', type.value)
    // console.log('id:', id.value)
}

const isExpired = () => {
    const currentData = new Date().getTime()
    if(currentData > new Date(parseInt(expireTime.value)).getTime()){
        isExpire.value = true
    }
    // console.log('是否过期:', isExpire.value)
}

const receiveBenefit = () => {
    if(serviceInfo.value){
        const confirmParams = {
            id: id.value,
            quantity: parseInt(quantity.value),
            serviceItem: serviceInfo.value,
            service_key: type.value
        }
        console.log('领取权益')
        console.log(userInfo.value)
        if(!userInfo.value){
            showToast({
                message: '用户未登录,即将跳转登录页',
                duration: 1000,
            })
            setTimeout(() => {
                router.push('/phone-login')
            },1000)
        }else{
            orderService.orderConfirm(confirmParams).then(res => {
                console.log('领取权益结果:', res)
                if(res.success){
                    showToast({
                        message: '领取成功',
                    })
                    isReceive.value = true 
                }else(
                    showToast({
                        message: res.errMsg,
                    })
                )
            })
        }
    }
}
onBeforeMount(() => {
    console.log('userInfo',userInfo.value)
})
onMounted(() => {
    init()
    isExpired()
})

</script>

<style scoped lang='scss'>
</style>
