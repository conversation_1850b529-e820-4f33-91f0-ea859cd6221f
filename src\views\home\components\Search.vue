<script lang="ts" setup>
import { useRouter } from 'vue-router'

const router = useRouter()
const toSearch = () => {
    router.push({ name: 'searchhistory' })
}
</script>

<template>
    <div class="home-search">
        <div class="flex flex-row gap-6 h-36 back-color-white top-bottom-center border-radius-4 lr-padding-12"
             @click="toSearch">
            <div class="w-16 h-16 flex top-bottom-center left-right-center font-16">
                <van-icon name="search" class="color-text-grey" />
            </div>
            <div class="font-14 color-text-grey lh-20">请输入您要检测的企业名称</div>
        </div>
    </div>
</template>

<style scoped>
.home-search {
    margin-bottom: 16px;
    padding-left: 16px;
    padding-right: 16px;
}

.icon {
    width: 10px;
    height: 10px;
}
</style>
