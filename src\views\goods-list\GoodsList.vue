<script lang="ts" setup>
import { useRoute, useRouter } from 'vue-router'
import { ComboGroupList, LeadsGroupList } from './components'
import { onBeforeMount, onMounted, ref } from 'vue'
import orderService from '@/service/orderService'
import { getItem, setItem } from '@/utils/storage'
import type { IOrderPrepPayparams } from '@/types/order'
import { closeToast, showLoadingToast } from 'vant'
const route = useRoute()
const router = useRouter()
const goods = route.query.goods
const code = route.query.code
const payResult = route.query.payResult
const orderId = route.query.orderId
const openidRef = ref('')

const getUrl = (appid: string, state: string, url: string) => {
    let oauthUrl = 'https://weixin.shuzutech.com/get-code-iIa39TfV.html'
    oauthUrl = oauthUrl + '?scope=snsapi_base'
    const appidParam = '&appid=' + appid
    const stateParam = '&state=' + state
    const redirectUriParam = '&redirect_uri=' + encodeURIComponent(url)
    const redirectUrl = oauthUrl + appidParam + stateParam + redirectUriParam

    return redirectUrl
}

const createOrder = ({ goodsId, quantity }: { goodsId: string; quantity: number }) => {
    showLoadingToast({
        message: '即将打开支付页面...',
        forbidClick: true,
    })
    orderService
        .goodsOrderPrep({
            goodsId: goodsId,
            openid: openidRef.value,
            quantity: quantity,
        })
        .then((res) => {
            if (res.errCode === 0) {
                console.log('createOrder 成功')
                doPay(res.data)
            }
        })
        .finally(() => {
            closeToast()
        })
}

const doPay = (data: IOrderPrepPayparams) => {
    const successUrl = encodeURIComponent(`${location.href}&payResult=success&orderId=${data.order_id}`)

    let url = 'http://weixin.shuzutech.com/jsapi-pay.html?'
    url = url + `appId=${data.appId}`
    url = url + `&timeStamp=${data.timeStamp}`
    url = url + `&nonceStr=${data.nonceStr}`
    url = url + `&package=${data.package}`
    url = url + `&signType=${data.signType}`
    url = url + `&paySign=${data.paySign}`
    url = url + `&successUrl=${successUrl}`

    window.location.href = url
}

const getOpenId = () => {
    if (!code) return
    orderService
        .goodsGetOpenid({
            code: code.toString(),
        })
        .then((res) => {
            if (res.errCode === 0) {
                setItem('openid', res.data.openid)
                openidRef.value = res.data.openid
            }
        })
}

onMounted(() => {
    const openid = getItem('openid')
    if (openid) {
        openidRef.value = openid
        return
    }
    if (code && !openid) {
        return getOpenId()
    }

    const currentUrl = document.location.origin + document.location.pathname + document.location.search
    const url = getUrl('wx70e22978a452adcb', 'STATE', currentUrl)
    window.open(url)
})

onBeforeMount(() => {
    if (!payResult) return
    if (payResult === 'success') {
        router.replace({
            name: 'myOrdersDetail',
            query: {
                orderId: orderId,
            },
        })
    }

    if (payResult === 'failed') {
        router.replace({
            name: 'myOrdersDetail',
            query: {
                orderId: orderId,
            },
        })
    }
})
</script>

<template>
    <div class="goods-list tb-padding-12 lr-padding-16 height-100">
        <LeadsGroupList v-if="goods === 'leads-group'" :openid="openidRef" :create-order="createOrder" />
        <ComboGroupList v-if="goods === 'combo-group'" :openid="openidRef" :create-order="createOrder" />
    </div>
</template>

<style lang="scss" scoped></style>
