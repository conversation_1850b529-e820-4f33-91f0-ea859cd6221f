<template>
    <div class="height-100 back-color-common lr-padding-16 display-flex flex-column space-between">
        <div>
            <div class="h-432 tb-margin-12 contain-top relative">
                <div class="absolute left-16 bottom-16">
                    <div class="!color-blue font-20">25积分奖励</div>
                    <div class="!color-blue font-14">首次登录奖励</div>
                </div>
                
            </div>
        </div>
      
        <div class="b-margin-12">
            <van-button type="primary" class="h-46 width-100 border-radius-8" style="background: linear-gradient(to right, #3c74eb, #95d5f4); border: none;" @click="handleReceiveAward()">领取奖励</van-button>
        </div>
    </div>
</template>
<script lang='ts' setup>
import { computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
const route = useRoute()
const token = localStorage.getItem('access_token')
const userId = computed(() => {
    const { query } = route
    const { userId } = query || {}
    return userId
})
const router = useRouter()
const handleReceiveAward = () => {
    // 判断是不是新用户 
    // 新用户去注册
    // 老用户去首页
    if (token) {
        router.push({
            name: 'home'
        })
    } else {
        router.push({
            name: 'phoneLogin',
            query: {
                inviter: userId.value
            }
        })
    }
}

onMounted(() => {
    console.log('userId',userId.value)
})

</script>

<style scoped lang='scss'>
.contain-top{
    background-image: url('@/assets/images/receive-award-bg.png');
    background-size: 100% 100%;
}
</style>
