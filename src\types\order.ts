import type { IAllRecord } from '@/types/record'
import type { ContactItem } from '@/types/company'
import type { ICommonResponse, IPaginationResponse } from './axios'

export interface IServiceOrderPageParams extends IAllRecord {
    page: number
    pageSize: number
    serviceKeys?: string | undefined
    hideExpire?: string
    hideValid?: string
}
export interface IServiceOrderResponseItemService {
    app_id: string
    created_at: string
    id: number
    service_desc: string
    service_id: number
    service_name: string
    service_type: number
    should_masking: number
    status: number
    unit: string
    unit_quantity: number
    unit_type: number
    updated_at: string
}
export interface IServiceOrderResponseItem {
    app_id: string
    balance: number
    channel_id: number
    created_at: string
    expire_time: string
    id: number
    is_allow_negative: number
    is_expired: boolean
    out_order_id: string
    quantity: number
    service: IServiceOrderResponseItemService
    service_id: number
    unit_type: number
    updated_at: string
    user_id: string
}
export interface IServiceOrderResponse {
    data: IServiceOrderResponseItem[]
    errCode: number
    firstPage: boolean
    hasNextPage: boolean
    hasPreviousPage: boolean
    lastPage: boolean
    page: number
    pageSize: number
    success: boolean
    total: number
    totalPages: number
}
export interface IOrderServiceStatisticsParams extends IAllRecord {
    serviceKeys: string
    tenantId?: string
}

export interface IOrderBuyLegalResponse extends ICommonResponse {
    contacts: ContactItem[]
    contactNum: number
}

export interface IOrderParams extends IAllRecord {
    serviceKey: string
    socialCreditCode: string
    companyName: string
    orderId?: number
}

export interface IOrderCheckEntBuyResponse {
    status: string
    totalBalance: string
    optionId: string
}

export interface IOrderCheckEntBuyResponseArr {
    data: [
        {
            optionId: string
            status: string
            expireTime: string
        },
    ]
}

export interface IOrderCheckEntBuyParams extends IAllRecord {
    socialCreditCode: string
}

export interface IOrderInviteRecordParams {
    page: number
    pageSize: number
    userId?: string
    tenantId?: string
    type?: number
}

export interface IOrderBuyLegalResponse {
    data: []
    errCode: number
    firstPage: boolean
    hasNextPage: boolean
    hasPreviousPage: boolean
    lastPage: boolean
    page: number
    pageSize: number
    success: boolean
    total: number
    totalPages: number
}
export interface IOrderPaymentListRequest extends IAllRecord {
    page: number
    pageSize: number
    queryService?: string
    orderStatus: string
}

export interface IOrderPaymentListItemPage extends IPaginationResponse {
    data: IOrderPaymentListItem[]
}

export interface IOrderPaymentListItem {
    id: number
    out_order_id: string
    out_trade_no: string
    transaction_id: string
    quantity: number
    order_name: string
    mch_id: string
    payment_way: string
    payment_type: string
    order_status: string
    goods_id: number
    snapshot_id: number
    actual_amount: number
    credit_amount: number
    bill_amount: number
    total_bill_amount: number
    discount_amount: number
    extra_discount_percent: string
    extra_discount_amount: number
    total_discount_amount: number
    invoice_amount: number
    app_id: string
    extra_info: string
    delay_time: string
    time_end: string
    notify_url: string
    user_id: string
    channel_id: 1
    order_for: 2
    notice_url: string
    created_at: string
    updated_at: string
    order_status_str: string
    payment_way_str: string
    service_order: []
}

export interface IOrderPaymentCancel {
    orderId: string
}

export interface IOrderGoodsDetailRequest extends IAllRecord {
    orderId: string
}

export interface IOrderGoodsDetailResponse extends ICommonResponse {
    data: IOrderPaymentListItem
}

export interface IOrderGoodsOpenidRequest extends IAllRecord {
    code: string
}

export interface IOrderGoodsOpenidResponse extends ICommonResponse {
    data: {
        openid: string
    }
}

export interface IOrderPrepRequest {
    goodsId: string
    openid: string
    quantity: number
}

export interface IOrderPrepResponse extends ICommonResponse {
    data: IOrderPrepPayparams
}

export interface IOrderPrepPayparams extends ICommonResponse {
    appId: string
    timeStamp: string
    nonceStr: string
    package: string
    signType: string
    paySign: string
    order_id: string
    partnerid: string
    order_status: string
}

export interface IOrderGoodsServiceGoodsPageRequest extends IAllRecord {
    page: number
    pageSize: number
    serviceKey: string
}

export interface IOrderServicePageItem {
    id: number
    category_id: number
    goods_id: 'string'
    priority: number
    created_at: 'string'
    updated_at: 'string'
    goods: {
        id: number
        goods_name: 'string'
        goods_desc: 'string'
        goods_amount: number
        goods_discount: number
        goods_expire_time: 'string'
        is_allow_multiple: number
        is_allow_extend: number
        is_allow_negative: number
        goods_type: number
        goods_status: number
        purchase_rule: 'string'
        created_at: 'string'
        updated_at: 'string'
    }
    category: {
        id: number
        category_name: 'string'
        app_id: 'string'
        created_at: 'string'
        updated_at: 'string'
    }
    service: [
        {
            id: number
            goods_id: 'string'
            service_id: number
            quantity: 'string'
            unit: 'string'
            unit_type: number
            exp_quantity: number
            amount: number
            updated_at: 'string'
            created_at: 'string'
            service_item: {
                id: number
                service_id: number
                service_name: 'string'
                service_desc: 'string'
                service_type: number
                app_id: 'string'
                unit: 'string'
                unit_type: number
                unit_quantity: number
                should_masking: number
                status: number
                created_at: 'string'
                updated_at: 'string'
            }
        },
    ]
}

export interface IOrderServicePageResponse extends IPaginationResponse {
    data: IOrderServicePageItem[]
}

export interface IOrderCheckUserPointsResponse extends ICommonResponse {
    id: string
    tenantId: string
    orgId: string
    balance: number
}

export interface IOrderInvitedSummaryResponse extends ICommonResponse {
    number: number
    totalPoints: number
}

export interface IOrderGoodsListItem {
    id: string
    name: string
    points: number
    goodsId: string
}
export interface IOrderGoodsList extends ICommonResponse {
    data: IOrderGoodsListItem[]
}

interface goodsItem {
    id: string
    name: string
    points: number
    goodsId: string
}
export interface IOrderInviteRecordListItem {
    accountId: string
    amount: number
    createTime: number
    id: string
    type: number
    relationUser?: string
    relationUserName?: string
    relationGoods?: string
    msg?: string
    goods?: goodsItem[]
    relationUserMobile?: string
}

export interface IOrderInviteRecordList extends IPaginationResponse {
    data: IOrderInviteRecordListItem[]
}

export interface IOrderUsageRecordParams extends IAllRecord {
    page: number
    pageSize: number
    serviceOrderId: string
}

export interface IOrderUsageRecordResponseItem {
    app_id: string
    belong: number
    change_type: string
    channel_id: number
    created_at: string
    expire_time: string
    extra_info: string
    id: number
    option_id: string
    quantity: number
    service: IServiceOrderResponseItemService
    service_id: number
    service_order_id: number
    start_time: string
    updated_at: string
    user_id: string
    isExpanded?: boolean
}
export interface IOrderUsageRecordResponse {
    data: IOrderUsageRecordResponseItem[]
    errCode: number
    firstPage: boolean
    hasNextPage: boolean
    hasPreviousPage: boolean
    lastPage: boolean
    page: number
    pageSize: number
    success: boolean
    total: number
    totalPages: number
}

export interface IOrderRePay {
    orderId: string
}
export interface IOrderPredeductRequest {
    id: string
    quantity: number
    serviceItem: IOrderPredeductRequestItem
    service_key: string
}

export interface IOrderPredeductRequestItem {
    app_id: string
    balance: number
    channel_id: number
    created_at: string
    expire_time: string
    id: number
    is_allow_negative: number
    is_expired: boolean
    out_order_id: string
    quantity: number
    service: IServiceOrderResponseItemService
    service_id: number
    unit_type: number
    updated_at: string
    user_id: string
}
export interface IOrderPredeductResponse extends ICommonResponse {
    data:{id: string}
}
