<script lang="ts" setup>
import { computed } from 'vue'
import { Brand, Desc, InnerTest, MainImg, Search, Social } from './components'
import HomeSwipe from './components/HomeSwipe.vue'
import { tabbarheight } from '@/utils/tabbar-height'

const paddingBottom = computed(() => {
    return tabbarheight() + 'px'
})
</script>

<template>
    <div class="home">
        <div class="wrap">
            <Brand />
            <MainImg />
            <Search />
            <Desc />
            <HomeSwipe />
            <Social />
            <InnerTest />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.home {
    height: 100%;
    background: linear-gradient(112.61deg, #3c74eb 15.3%, #95d5f4 89.95%);
    overflow: scroll;
    padding-bottom: v-bind(paddingBottom);
}

.wrap {
    background-image: url('@/assets/images/home/<USER>');
    background-position: center;
    background-repeat: no-repeat;
    background-position-y: -42px;
    background-size: contain;
    padding-top: 44px;
    margin-bottom: 24px;
}
</style>
