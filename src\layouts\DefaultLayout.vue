<script setup lang="ts">
import userService from '@/service/userService'
import { getItem } from '@/utils/storage'
import { onBeforeMount } from 'vue'
import { useStore } from 'vuex'
const store = useStore()

onBeforeMount(() => {
    const accessToken = getItem('access_token')
    if (accessToken) {
        userService.userGetAccountInfo().then((account) => {
            store.dispatch('user/setAccountInfo', { ...account })
        })
    }
})
</script>

<template>
    <router-view v-slot="{ Component, route }">
        <keep-alive>
            <component :is="Component" :key="route.name" v-if="route.meta.keepAlive" />
        </keep-alive>
        <component :is="Component" :key="route.name" v-if="!route.meta.keepAlive" />
    </router-view>
</template>

<style lang="scss" scoped></style>
