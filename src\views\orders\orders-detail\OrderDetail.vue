<script lang="ts" setup>
import { useRoute } from 'vue-router'
import { OrderAction, OrderFeeInfo, OrderGoodsInfo, OrderInfo, OrderStatus } from './components'
import { onMounted, ref } from 'vue'
import orderService from '@/service/orderService'
import type { IOrderPaymentListItem, IOrderPrepPayparams } from '@/types/order'
import { closeToast, showFailToast, showLoadingToast } from 'vant'
const route = useRoute()
const orderId = route.query.orderId
const orderDetail = ref<IOrderPaymentListItem | null>(null)
const loading = ref(true)

const getDetail = () => {
    if (!orderId) return
    orderService
        .goodsOrderDetail({
            orderId: orderId.toString(),
        })
        .then((res) => {
            loading.value = false
            if (res.errCode === 0) {
                orderDetail.value = res.data
            }
        })
}

const createOrder = () => {
    if (!orderDetail.value) return
    showLoadingToast({
        message: '即将打开支付页面...',
        forbidClick: true,
    })

    orderService
        .goodsOrderRepay({
            orderId: orderDetail.value?.out_order_id,
        })
        .then((res) => {
            const { errCode, errMsg } = res

            if (errCode === 0) {
                console.log('createOrder 成功')
                doPay(res.data)
            } else {
                showFailToast(errMsg || '无法打开支付页面，请稍后再试')
            }
        })
        .finally(() => {
            closeToast()
        })
}

const doPay = (data: IOrderPrepPayparams) => {
    const successUrl = encodeURIComponent(`${location.href}&payResult=success&orderId=${data.order_id}`)

    let url = 'http://weixin.shuzutech.com/jsapi-pay.html?'
    url = url + `appId=${data.appId}`
    url = url + `&timeStamp=${data.timeStamp}`
    url = url + `&nonceStr=${data.nonceStr}`
    url = url + `&package=${data.package}`
    url = url + `&signType=${data.signType}`
    url = url + `&paySign=${data.paySign}`
    url = url + `&successUrl=${successUrl}`

    window.location.href = url
}

onMounted(() => {
    getDetail()
})
</script>

<template>
    <template v-if="loading">
        <div class="tb-padding-12">
            <van-skeleton title :row="3" />
        </div>
    </template>
    <template v-if="!loading">
        <div class="order-detail lr-padding-16 tb-padding-12 back-color-main height-100 flex flex-column gap-12">
            <OrderStatus :detail="orderDetail" />
            <OrderGoodsInfo :detail="orderDetail" />
            <OrderInfo :detail="orderDetail" />
            <OrderFeeInfo :detail="orderDetail" />
            <OrderAction :detail="orderDetail" :create-order="createOrder" />
        </div>
    </template>
</template>

<style lang="scss" scoped></style>
