<script lang="ts" setup>
import { computed, onMounted } from 'vue'
import { LoginBrand, LoginPasswordForm, LoginPhoneBind, LoginSwitch } from './components'
import { useRoute, useRouter } from 'vue-router'
import LoginPhoneForm from './components/LoginPhoneForm.vue'
// import { loadScript } from '@/utils/loadJs'
import { getItem } from '@/utils/storage'

const route = useRoute()
const router = useRouter()

// 通过路由 name 判断
const isPasswordLogin = computed(() => route.name === 'passwordLogin')
const isPhoneLogin = computed(() => route.name === 'phoneLogin')
const isPhoneBind = computed(() => route.name === 'phoneBind')

const checkLogin = () => {
    const accessToken = getItem('access_token')
    if (accessToken) return router.push('/')
}

onMounted(() => {
    checkLogin()
    // loadScript('/static/tac/load.min.js')
})
</script>

<template>
    <div class="login-view">
        <div class="flex flex-column wrap t-padding-38 lr-padding-16 height-100">
            <LoginBrand />
            <LoginSwitch />
            <div class="login-form">
                <div
                    class="height-100 flex-1 back-color-white"
                    :class="{
                        'right-radius': isPhoneLogin,
                        'left-radius': isPasswordLogin,
                    }"
                >
                    <LoginPasswordForm v-if="isPasswordLogin" />
                    <LoginPhoneForm v-if="isPhoneLogin" />
                    <LoginPhoneBind v-if="isPhoneBind" />
                </div>
            </div>
        </div>
        <div id="captcha" class="captcha"></div>
    </div>
</template>

<style lang="scss" scoped>
.login-view {
    height: 100%;
    background-image: url('@/assets/images/login/bg.jpg');
    background-position: right;
    background-repeat: no-repeat;
    background-position-y: -95px;
    background-size: cover;
    position: relative;
}

.right-radius {
    border-top-right-radius: 8px;
}

.left-radius {
    border-top-left-radius: 8px;
}

.login-form {
    background: #ffffff4d;
}

.captcha {
    position: absolute;
    width: 100%;
    display: flex;
    justify-content: center;
    top: 142px;
}
</style>
